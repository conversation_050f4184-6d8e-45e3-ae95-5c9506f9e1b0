<?php
/**
 * Test script to check SchedSpot plugin activation
 */

// Simulate WordPress environment
define('ABSPATH', '/');
define('WP_DEBUG', true);

// Mock WordPress functions that might be called during plugin loading
function plugin_dir_path($file) {
    return dirname($file) . '/';
}

function plugins_url($path, $plugin = '') {
    if (empty($plugin)) {
        return 'http://example.com/wp-content/plugins' . $path;
    }
    return 'http://example.com/wp-content/plugins/' . basename(dirname($plugin)) . $path;
}

function untrailingslashit($string) {
    return rtrim($string, '/\\');
}

function admin_url($path, $scheme = 'admin') {
    return 'http://example.com/wp-admin/' . $path;
}

function determine_locale() {
    return 'en_US';
}

function apply_filters($tag, $value) {
    return $value;
}

function unload_textdomain($domain) {
    return true;
}

function load_textdomain($domain, $mofile) {
    return true;
}

function load_plugin_textdomain($domain, $deprecated, $plugin_rel_path) {
    return true;
}

function plugin_basename($file) {
    return basename(dirname($file)) . '/' . basename($file);
}

function do_action($tag) {
    // Mock action
}

function register_activation_hook($file, $function) {
    // Mock hook registration
}

function register_deactivation_hook($file, $function) {
    // Mock hook registration
}

function add_action($tag, $function_to_add, $priority = 10, $accepted_args = 1) {
    // Mock action
}

function is_admin() {
    return true;
}

// Test loading the main plugin file
echo "Testing SchedSpot plugin loading...\n";

try {
    // Set up plugin constants
    define('SCHEDSPOT_PLUGIN_FILE', __DIR__ . '/schedspot/schedspot.php');
    
    // Include the main plugin file
    include_once __DIR__ . '/schedspot/schedspot.php';
    
    echo "✅ Plugin loaded successfully!\n";
    echo "✅ No fatal errors detected.\n";
    
} catch (ParseError $e) {
    echo "❌ Parse Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\nTest completed.\n";
